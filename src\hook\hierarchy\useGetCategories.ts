import { api } from "@/services/api";
import { GetAllCategoriesDto } from "@/utils/types/DTO/hierarchy/categories.dto";
import { useQuery } from "@tanstack/react-query";

type TGetAllCategories = {
  page?: number;
  limit?: number;
  searchTerm?: string;
};

async function getAllCategories(params: TGetAllCategories) {
  const { data } = await api.get<GetAllCategoriesDto>(`/management/hierarchy`, {
    params: {
      search: params.searchTerm,
      page: params.page,
      limit: params.limit,
    },
  });
  return data;
}

export function useGetAllCategories(params: TGetAllCategories) {
  return useQuery({
    queryKey: ["categories", params],
    queryFn: async () => await getAllCategories(params),
  });
}
