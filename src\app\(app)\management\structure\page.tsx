"use client";
import Default<PERSON>utton from "@/components/global/buttons/button";
import BasicModal from "@/components/global/modal/basic-modal";
import {
  Box,
  Flex,
  HStack,
  Stack,
  Table,
  Text,
  VStack,
  Pagination,
  ButtonGroup,
  IconButton,
} from "@chakra-ui/react";
import FormCombobox, {
  ComboboxOption,
} from "@/components/global/combobox/form-combobox";
import {
  LuPlus,
  LuTrash2,
  LuChevronLeft,
  LuChevronRight,
} from "react-icons/lu";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { formatInTimeZone } from "date-fns-tz";
import { useGetAllCategories } from "@/hook/hierarchy/useGetCategories";
import { toaster } from "@/components/ui/toaster";
import { api } from "@/services/api";
import { useMutation } from "@tanstack/react-query";
import { useGetAllStructures } from "@/hook/hierarchy/useGetStructures";
import { queryClient } from "@/services/queryClient";
import { GetStructureDto } from "@/utils/types/DTO/hierarchy/structures.dto";

const NewStructureSchema = yup.object().shape({
  segmentId: yup.string().required("O segmento é obrigatório"),
  superintendencyId: yup.string().required("A superintendência é obrigatória"),
  managementId: yup.string().required("A gerência é obrigatória"),
  positionIds: yup
    .array()
    .of(yup.string().required())
    .min(1, "Pelo menos um cargo é obrigatório")
    .required("Os cargos são obrigatórios"),
});

type NewStructureFormData = yup.InferType<typeof NewStructureSchema>;

export default function Structure() {
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedStructure, setSelectedStructure] =
    useState<GetStructureDto | null>(null);
  const [page, setPage] = useState(1);

  const { data: categoriesData } = useGetAllCategories({});

  const { data: structureData } = useGetAllStructures({
    page,
    limit: 6,
  });

  const getSegmentOptions = (): ComboboxOption[] => {
    return (
      categoriesData?.data
        ?.filter((cat) => cat.type === "SEGMENT")
        .map((cat) => ({
          label: cat.name,
          value: cat.secureId,
        })) || []
    );
  };

  const getSuperintendencyOptions = (): ComboboxOption[] => {
    return (
      categoriesData?.data
        ?.filter((cat) => cat.type === "SUPERINTENDENCY")
        .map((cat) => ({
          label: cat.name,
          value: cat.secureId,
        })) || []
    );
  };

  const getManagementOptions = (): ComboboxOption[] => {
    return (
      categoriesData?.data
        ?.filter((cat) => cat.type === "MANAGEMENT")
        .map((cat) => ({
          label: cat.name,
          value: cat.secureId,
        })) || []
    );
  };

  const getPositionOptions = (): ComboboxOption[] => {
    return (
      categoriesData?.data
        ?.filter((cat) => cat.type === "POSITION")
        .map((cat) => ({
          label: cat.name,
          value: cat.secureId,
        })) || []
    );
  };

  const {
    handleSubmit,
    reset,
    setValue,
    watch,
    formState: { errors, isSubmitting },
  } = useForm<NewStructureFormData>({
    resolver: yupResolver(NewStructureSchema),
    defaultValues: {
      segmentId: "",
      superintendencyId: "",
      managementId: "",
      positionIds: [],
    },
  });

  const handleOpenAddModal = () => {
    reset();
    setIsAddModalOpen(true);
  };

  const handleOpenDeleteModal = (structure: GetStructureDto) => {
    setSelectedStructure(structure);
    setIsDeleteModalOpen(true);
  };

  const addStructure = useMutation({
    mutationFn: async (data: NewStructureFormData) => {
      await api.post("/management/hierarchy-structure", data);
    },
    onSuccess: () => {
      toaster.success({
        title: "Estrutura adicionada com sucesso!",
      });
    },
  });

  const deleteStructure = useMutation({
    mutationFn: async () => {
      await api.delete(
        `/management/hierarchy-structure/${selectedStructure?.secureId}`
      );
    },
    onSuccess: () => {
      toaster.success({
        title: "Estrutura excluída com sucesso!",
      });
    },
  });

  const handleAddStructure = async (data: NewStructureFormData) => {
    try {
      await addStructure.mutateAsync(data);
      queryClient.invalidateQueries({ queryKey: ["structures"] });
      setIsAddModalOpen(false);
      reset();
    } catch (e) {
      console.log("Erro ao adicionar estrutura", e);
    }
  };

  const handleDeleteStructure = async () => {
    try {
      await deleteStructure.mutateAsync();
      queryClient.invalidateQueries({ queryKey: ["structures"] });
      setIsDeleteModalOpen(false);
    } catch (e) {
      console.log("Erro ao excluir estrutura", e);
    }
  };

  return (
    <Flex flex={1} position={"relative"} overflow={"hidden"} p={4}>
      <Stack w="100%" gap={6}>
        {/* Header */}
        <Text fontSize="2xl" fontWeight="bold" color="white">
          Estrutura Hierárquica
        </Text>

        {/* Search and Filters */}
        <HStack gap={4} justify="end">
          <DefaultButton onClick={handleOpenAddModal} size="md">
            <LuPlus />
            Adicionar Estrutura
          </DefaultButton>
        </HStack>

        {/* Table */}
        <Box
          bg="gray.800"
          borderRadius="lg"
          border="1px solid"
          borderColor="gray.600"
          overflowX="auto"
        >
          <Table.Root size="md" variant="outline">
            <Table.Header bg="gray.700">
              <Table.Row>
                <Table.ColumnHeader color="white" fontWeight="bold">
                  Seguimento
                </Table.ColumnHeader>
                <Table.ColumnHeader color="white" fontWeight="bold">
                  Superintendência
                </Table.ColumnHeader>
                <Table.ColumnHeader color="white" fontWeight="bold">
                  Gerência
                </Table.ColumnHeader>
                <Table.ColumnHeader color="white" fontWeight="bold">
                  Cargo
                </Table.ColumnHeader>
                <Table.ColumnHeader color="white" fontWeight="bold">
                  Data de Criação
                </Table.ColumnHeader>
                <Table.ColumnHeader color="white" fontWeight="bold">
                  <Flex justify="center">Ações</Flex>
                </Table.ColumnHeader>
              </Table.Row>
            </Table.Header>

            <Table.Body>
              {structureData?.data && structureData?.data.length > 0 ? (
                structureData?.data.map((item) => (
                  <Table.Row key={item.secureId} _hover={{ bg: "gray.700" }}>
                    <Table.Cell>
                      <Text color="gray.300">{item.segment}</Text>
                    </Table.Cell>
                    <Table.Cell>
                      <Text color="gray.300">{item.superintendency}</Text>
                    </Table.Cell>
                    <Table.Cell>
                      <Text color="gray.300">{item.management}</Text>
                    </Table.Cell>
                    <Table.Cell>
                      <Text color="gray.300">{item.position}</Text>
                    </Table.Cell>
                    <Table.Cell>
                      <Text color="gray.300">
                        {formatInTimeZone(
                          item.createdAt,
                          "America/Sao_Paulo",
                          "dd/MM/yyyy HH:mm:ss"
                        )}
                      </Text>
                    </Table.Cell>
                    <Table.Cell>
                      <HStack gap={2} justifyContent={"center"}>
                        <DefaultButton
                          tooltipContent="Excluir"
                          buttonColor="red.500"
                          size="sm"
                          onClick={() => handleOpenDeleteModal(item)}
                        >
                          <LuTrash2 />
                        </DefaultButton>
                      </HStack>
                    </Table.Cell>
                  </Table.Row>
                ))
              ) : (
                <Table.Row>
                  <Table.Cell colSpan={5}>
                    <Text color={"gray.400"} textAlign={"center"} py={4}>
                      Nenhuma estrutura hierárquica encontrada
                    </Text>
                  </Table.Cell>
                </Table.Row>
              )}
            </Table.Body>
          </Table.Root>
        </Box>

        {/* Pagination */}
        {structureData?.meta && structureData?.meta.TotalPages > 0 && (
          <Flex justifyContent={"flex-end"} mr={10}>
            <Pagination.Root
              count={structureData?.meta.TotalItems}
              pageSize={structureData?.meta.ItemsPerPage}
              page={structureData?.meta.CurrentPage}
              onPageChange={(e) => setPage(e.page)}
            >
              <ButtonGroup variant="ghost" size="sm">
                <Pagination.PrevTrigger asChild>
                  <IconButton arial-label="Página anterior">
                    <LuChevronLeft />
                  </IconButton>
                </Pagination.PrevTrigger>

                <Pagination.Items
                  render={(pageItem) => (
                    <IconButton
                      key={pageItem.value}
                      variant={pageItem.value === page ? "outline" : "ghost"}
                      aria-label={`Ir para a página ${pageItem.value}`}
                      borderColor={
                        pageItem.value === page ? "gray.400" : "transparent"
                      }
                      _hover={{
                        borderColor:
                          pageItem.value === page ? "blue.300" : "gray.600",
                      }}
                      _active={{
                        borderColor:
                          pageItem.value === page ? "blue.500" : "gray.700",
                      }}
                    >
                      {pageItem.value}
                    </IconButton>
                  )}
                />

                <Pagination.NextTrigger asChild>
                  <IconButton aria-label="Próxima página">
                    <LuChevronRight />
                  </IconButton>
                </Pagination.NextTrigger>
              </ButtonGroup>
            </Pagination.Root>
          </Flex>
        )}

        {/* {totalItems > 0 && (
					<Text color="gray.400" fontSize="sm" textAlign="center">
							Mostrando {startPages + 1} a {Math.min(endPages, totalItems)} de {totalItems} estrutura hierárquica
						{searchTerm && ` (filtrado)`}
					</Text>
				)}*/}
      </Stack>

      {/* Add Structure Modal */}
      <BasicModal
        open={isAddModalOpen}
        setOpen={setIsAddModalOpen}
        title="Adicionar Nova Estrutura"
        size="lg"
        asForm={true}
        handleSubmit={handleSubmit(handleAddStructure)}
        isSubmitting={isSubmitting}
        confirmText="Adicionar"
        cancelText="Cancelar"
        placement="center"
      >
        <VStack gap={4} align="stretch">
          <FormCombobox
            label="Segmento"
            placeholder="Selecione um segmento"
            options={getSegmentOptions()}
            value={watch("segmentId")}
            onValueChange={(value) => setValue("segmentId", value as string)}
            error={errors.segmentId}
            isInvalid={!!errors.segmentId}
          />

          <FormCombobox
            label="Superintendência"
            placeholder="Selecione uma superintendência"
            options={getSuperintendencyOptions()}
            value={watch("superintendencyId")}
            onValueChange={(value) =>
              setValue("superintendencyId", value as string)
            }
            error={errors.superintendencyId}
            isInvalid={!!errors.superintendencyId}
          />

          <FormCombobox
            label="Gerência"
            placeholder="Selecione uma gerência"
            options={getManagementOptions()}
            value={watch("managementId")}
            onValueChange={(value) => setValue("managementId", value as string)}
            error={errors.managementId}
            isInvalid={!!errors.managementId}
          />

          <FormCombobox
            label="Cargos"
            placeholder="Selecione os cargos"
            options={getPositionOptions()}
            value={watch("positionIds")}
            onValueChange={(value) =>
              setValue("positionIds", value as string[])
            }
            error={errors.positionIds as any}
            isInvalid={!!errors.positionIds}
            multiple={true}
          />
        </VStack>
      </BasicModal>

      {/* Delete Structure Modal */}
      <BasicModal
        open={isDeleteModalOpen}
        setOpen={setIsDeleteModalOpen}
        title="Excluir Estrutura"
        size="sm"
        handleConfirm={handleDeleteStructure}
        confirmText="Excluir"
        cancelText="Cancelar"
        placement="center"
        confirmButtonColor="red.500"
      >
        <VStack gap={4} align="center">
          <Text fontSize="md" color="white" textAlign="center">
            Você tem certeza que deseja excluir esta estrutura organizacional?
          </Text>
          <VStack gap={2} align="center">
            <Text fontSize="sm" color="gray.300">
              <Text as="span" fontWeight="bold">
                Segmento:
              </Text>{" "}
              {selectedStructure?.segment}
            </Text>
            <Text fontSize="sm" color="gray.300">
              <Text as="span" fontWeight="bold">
                Superintendência:
              </Text>{" "}
              {selectedStructure?.superintendency}
            </Text>
            <Text fontSize="sm" color="gray.300">
              <Text as="span" fontWeight="bold">
                Gerência:
              </Text>{" "}
              {selectedStructure?.management}
            </Text>
            <Text fontSize="sm" color="gray.300">
              <Text as="span" fontWeight="bold">
                Cargo:
              </Text>{" "}
              {selectedStructure?.position}
            </Text>
          </VStack>
          <Text fontSize="sm" color="gray.400" textAlign="center">
            Esta ação não pode ser desfeita.
          </Text>
        </VStack>
      </BasicModal>
    </Flex>
  );
}
