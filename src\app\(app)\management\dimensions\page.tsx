"use client";

import Default<PERSON>utton from "@/components/global/buttons/button";
import BasicModal from "@/components/global/modal/basic-modal";
import {
  Box,
  Flex,
  HStack,
  Input,
  Stack,
  Table,
  Text,
  VStack,
  Field,
  useFilter,
  useListCollection,
} from "@chakra-ui/react";
import { LuPencil } from "react-icons/lu";
import * as yup from "yup";
import { queryClient } from "@/services/queryClient";
import { useMutation } from "@tanstack/react-query";
import { useForm } from "react-hook-form";
import { toaster } from "@/components/ui/toaster";
import { useState } from "react";
import { yupResolver } from "@hookform/resolvers/yup";
import { formatInTimeZone } from "date-fns-tz";
import { api } from "@/services/api";
import { GetDimensionsDto } from "@/utils/types/DTO/pillars/dimensions.dto";
import { useGetAllDimensions } from "@/hook/pillars/useGetAllDimensions";

const NewDimensionsSchema = yup.object().shape({
  name: yup.string().required("O nome é obrigatório"),
});

type DimensionsFormData = {
  name: string;
};

export default function Dimensions() {
  const [searchTerm, setSearchTerm] = useState("");
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedDimension, setSelectedDimension] =
    useState<GetDimensionsDto | null>(null);

  const { data: dimensionData } = useGetAllDimensions(searchTerm);

  const { contains } = useFilter({ sensitivity: "base" });

  const { collection, filter } = useListCollection({
    initialItems: [],
    filter: contains,
  });

  const {
    register,
    handleSubmit,
    reset,
    setValue,
    formState: { errors, isSubmitting },
  } = useForm({
    resolver: yupResolver(NewDimensionsSchema),
    defaultValues: {
      name: "",
    },
  });

  const editDimension = useMutation({
    mutationFn: async (data: DimensionsFormData) => {
      await api.patch(
        `/management/dimensions/${selectedDimension?.secureId}`,
        data
      );
    },
    onSuccess: () => {
      toaster.success({
        title: "Dimensão editada com sucesso!",
      });
    },
  });

  const handleOpenEditModal = (dimensions: GetDimensionsDto) => {
    filter("");
    setSelectedDimension(dimensions);
    setValue("name", dimensions.name);
    setIsEditModalOpen(true);
  };

  const handleEditDimensions = async (data: DimensionsFormData) => {
    try {
      await editDimension.mutateAsync(data);
      queryClient.invalidateQueries({ queryKey: ["dimensions"] });
      setIsEditModalOpen(false);
      filter("");
      reset();
    } catch (e) {
      console.log("Erro ao editar dimensão", e);
    }
  };

  return (
    <Flex flex={1} position={"relative"} overflow={"hidden"} p={4}>
      <Stack w={"100%"} gap={6}>
        <Text fontSize={"2xl"} fontWeight={"bold"} color={"white"}>
          Dimensões
        </Text>

        {/* 
			Search and Filters 
				<HStack gap={4} justify={"space-between"}>
          <Box position={"relative"} flex={1} maxW={"400px"}>
            <Input
              placeholder={"Buscar..."}
              bg={"gray.800"}
              border={"1px solid"}
              borderColor={"gray.600"}
              color={"white"}
              _placeholder={{ color: "gray.400" }}
              _focus={{
                borderColor: "blue.400",
                boxShadow: "0 0 0 1px #3182ce",
              }}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            <Box
              position="absolute"
              right={3}
              top="50%"
              transform="translateY(-50%)"
              color="gray.400"
            >
              <LuSearch />
            </Box>
          </Box>
					 <DefaultButton onClick={handleOpenAddModal} size="md">
            <LuPlus />
            Adicionar Dimensão
          </DefaultButton> 
        </HStack> 
			*/}

        {/* Table */}
        <Box
          bg={"gray.800"}
          border={"1px solid"}
          borderColor={"gray.600"}
          borderRadius={"lg"}
          overflowX={"auto"}
        >
          <Table.Root size={"md"} variant={"outline"}>
            <Table.Header bg={"gray.700"}>
              <Table.Row>
                <Table.ColumnHeader color={"white"} fontWeight={"bold"}>
                  Dimensões
                </Table.ColumnHeader>
                <Table.ColumnHeader color={"white"} fontWeight={"bold"}>
                  Data de Criação
                </Table.ColumnHeader>
                <Table.ColumnHeader color={"white"} fontWeight={"bold"}>
                  <Flex justify="center">Ações</Flex>
                </Table.ColumnHeader>
              </Table.Row>
            </Table.Header>
            <Table.Body>
              {dimensionData?.data?.map((item: GetDimensionsDto) => (
                <Table.Row key={item.secureId} _hover={{ bg: "gray.700" }}>
                  <Table.Cell>
                    <Text color={"gray.300"} fontWeight={"medium"}>
                      {item.name}
                    </Text>
                  </Table.Cell>
                  <Table.Cell>
                    <Text color={"gray.300"} fontWeight={"medium"}>
                      {formatInTimeZone(
                        item.createdAt,
                        "America/Sao_Paulo",
                        "dd/MM/yyyy HH:mm:ss"
                      )}
                    </Text>
                  </Table.Cell>
                  <Table.Cell>
                    <HStack gap={2} justifyContent={"center"}>
                      <DefaultButton
                        tooltipContent="Editar"
                        buttonColor="#156082"
                        size={"sm"}
                        onClick={() => handleOpenEditModal(item)}
                      >
                        <LuPencil />
                      </DefaultButton>
                    </HStack>
                  </Table.Cell>
                </Table.Row>
              )) || (
                <Table.Row>
                  <Table.Cell>
                    <Text color={"gray.400"} textAlign={"center"} py={4}>
                      Nenhuma dimensão encontrada
                    </Text>
                  </Table.Cell>
                </Table.Row>
              )}
            </Table.Body>
          </Table.Root>
        </Box>
      </Stack>

      <BasicModal
        open={isEditModalOpen}
        setOpen={setIsEditModalOpen}
        title="Editar Dimensão"
        size="md"
        asForm={true}
        handleSubmit={handleSubmit(handleEditDimensions)}
        isSubmitting={isSubmitting}
        confirmText="Salvar"
        cancelText="Cancelar"
        placement="center"
      >
        <VStack gap={4} align={"stretch"}>
          <Field.Root invalid={!!errors.name}>
            <Field.Label color={"white"}>Nome da Dimensão</Field.Label>
            <Input
              placeholder="Digite o nome da dimensão"
              bg={"gray.700"}
              border={"1px solid"}
              borderColor={"gray.600"}
              color={"white"}
              _placeholder={{ color: "gray.400" }}
              _focus={{
                borderColor: "blue.400",
                boxShadow: "0 0 0 1px #3182ce",
              }}
              {...register("name")}
            />
            <Field.ErrorText>{errors.name?.message}</Field.ErrorText>
          </Field.Root>
        </VStack>
      </BasicModal>
    </Flex>
  );
}
