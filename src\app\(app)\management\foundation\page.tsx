"use client";

import Default<PERSON>utton from "@/components/global/buttons/button";
import BasicModal from "@/components/global/modal/basic-modal";
import {
  Box,
  Flex,
  HStack,
  Input,
  Stack,
  Table,
  Text,
  VStack,
  Field,
  useFilter,
  useListCollection,
  Pagination,
  ButtonGroup,
  IconButton,
} from "@chakra-ui/react";
import {
  LuSearch,
  LuPencil,
  LuChevronLeft,
  LuChevronRight,
} from "react-icons/lu";
import * as yup from "yup";
import { queryClient } from "@/services/queryClient";
import { useMutation } from "@tanstack/react-query";
import { useForm } from "react-hook-form";
import { toaster } from "@/components/ui/toaster";
import { useState } from "react";
import { yupResolver } from "@hookform/resolvers/yup";
import { formatInTimeZone } from "date-fns-tz";
import { api } from "@/services/api";
import { GetFoundationDto } from "@/utils/types/DTO/pillars/foundation.dto";
import { useGetAllFoundation } from "@/hook/pillars/usegetAllFoundation";

const NewFoundationSchema = yup.object().shape({
  name: yup.string().required("O nome é obrigatório"),
  dimension: yup.string().optional(),
});

type FoundationFormData = {
  name: string;
  dimension?: string;
};

export default function Foundation() {
  const [searchTerm, setSearchTerm] = useState("");
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedFoundation, setSelectedFoundation] =
    useState<GetFoundationDto | null>(null);
  const [page, setPage] = useState(1);

  const { data: foundationData } = useGetAllFoundation({
    searchTerm,
    page,
    limit: 6,
  });

  const { contains } = useFilter({ sensitivity: "base" });

  const { collection, filter } = useListCollection({
    initialItems: [],
    filter: contains,
  });

  const {
    register,
    handleSubmit,
    reset,
    setValue,
    formState: { errors, isSubmitting },
  } = useForm({
    resolver: yupResolver(NewFoundationSchema),
    defaultValues: {
      name: "",
      dimension: "",
    },
  });

  const editFoundation = useMutation({
    mutationFn: async (data: FoundationFormData) => {
      await api.patch(
        `/management/foundations/${selectedFoundation?.secureId}`,
        data
      );
    },
    onSuccess: () => {
      toaster.success({
        title: "Fundamento editado com sucesso!",
      });
    },
  });

  const handleOpenEditModal = (foundation: GetFoundationDto) => {
    filter("");
    setSelectedFoundation(foundation);
    setValue("name", foundation.name);
    setValue("dimension", foundation.dimension);
    setIsEditModalOpen(true);
  };

  const handleEditFoundation = async (data: FoundationFormData) => {
    try {
      await editFoundation.mutateAsync(data);
      queryClient.invalidateQueries({ queryKey: ["foundation"] });
      setIsEditModalOpen(false);
      filter("");
      reset();
    } catch (e) {
      console.log("Erro ao editar fundamento", e);
    }
  };

  return (
    <Flex flex={1} position={"relative"} overflow={"hidden"} p={4}>
      <Stack w={"100%"} gap={6}>
        <Text fontSize={"2xl"} fontWeight={"bold"} color={"white"}>
          Fundamentos
        </Text>
        {/* Search and Filters */}
        <HStack gap={4} justify={"space-between"}>
          <Box position={"relative"} flex={1} maxW={"400px"}>
            <Input
              placeholder={"Buscar..."}
              bg={"gray.800"}
              border={"1px solid"}
              borderColor={"gray.600"}
              color={"white"}
              _placeholder={{ color: "gray.400" }}
              _focus={{
                borderColor: "blue.400",
                boxShadow: "0 0 0 1px #3182ce",
              }}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            <Box
              position="absolute"
              right={3}
              top="50%"
              transform="translateY(-50%)"
              color="gray.400"
            >
              <LuSearch />
            </Box>
          </Box>
        </HStack>
        {/* Table */}
        <Box
          bg={"gray.800"}
          border={"1px solid"}
          borderColor={"gray.600"}
          borderRadius={"lg"}
          overflowX={"auto"}
        >
          <Table.Root size={"md"} variant={"outline"}>
            <Table.Header bg={"gray.700"}>
              <Table.Row>
                <Table.ColumnHeader color={"white"} fontWeight={"bold"}>
                  Nome
                </Table.ColumnHeader>
                <Table.ColumnHeader color={"white"} fontWeight={"bold"}>
                  Dimensão
                </Table.ColumnHeader>
                <Table.ColumnHeader color={"white"} fontWeight={"bold"}>
                  Data de Criação
                </Table.ColumnHeader>
                <Table.ColumnHeader color={"white"} fontWeight={"bold"}>
                  <Flex justify="center">Ações</Flex>
                </Table.ColumnHeader>
              </Table.Row>
            </Table.Header>

            <Table.Body>
              {foundationData?.data && foundationData?.data.length > 0 ? (
                foundationData?.data.map((item: GetFoundationDto) => (
                  <Table.Row key={item.secureId} _hover={{ bg: "gray.700" }}>
                    <Table.Cell>
                      <Text color={"gray.300"} fontWeight={"medium"}>
                        {item.name}
                      </Text>
                    </Table.Cell>
                    <Table.Cell>
                      <Text color={"gray.300"} fontWeight={"medium"}>
                        {item.dimension}
                      </Text>
                    </Table.Cell>
                    <Table.Cell>
                      <Text color={"gray.300"} fontWeight={"medium"}>
                        {formatInTimeZone(
                          item.createdAt,
                          "America/Sao_Paulo",
                          "dd/MM/yyyy HH:mm:ss"
                        )}
                      </Text>
                    </Table.Cell>
                    <Table.Cell>
                      <HStack gap={2} justifyContent={"center"}>
                        <DefaultButton
                          tooltipContent="Editar"
                          buttonColor="#156082"
                          size={"sm"}
                          onClick={() => handleOpenEditModal(item)}
                        >
                          <LuPencil />
                        </DefaultButton>
                      </HStack>
                    </Table.Cell>
                  </Table.Row>
                ))
              ) : (
                <Table.Row>
                  <Table.Cell colSpan={3}>
                    <Text color={"gray.400"} textAlign={"center"} py={4}>
                      Nenhum fundamento encontrada
                    </Text>
                  </Table.Cell>
                </Table.Row>
              )}
            </Table.Body>
          </Table.Root>
        </Box>
        {/* Pagination */}
        {foundationData?.meta?.TotalItems &&
          foundationData?.meta.TotalPages > 1 && (
            <Flex justifyContent={"flex-end"} mr={10}>
              <Pagination.Root
                count={foundationData?.meta?.TotalItems || 0}
                pageSize={foundationData?.meta?.ItemsPerPage || 0}
                page={foundationData?.meta?.CurrentPage}
                onPageChange={(e) => setPage(e.page)}
              >
                <ButtonGroup variant="ghost" size="sm">
                  <Pagination.PrevTrigger asChild>
                    <IconButton aria-label="Página anterior">
                      <LuChevronLeft />
                    </IconButton>
                  </Pagination.PrevTrigger>

                  <Pagination.Items
                    render={(pageItem) => (
                      <IconButton
                        key={pageItem.value}
                        variant={pageItem.value === page ? "outline" : "ghost"}
                        aria-label={`Ir para a página ${pageItem.value}`}
                        borderColor={
                          pageItem.value === page ? "gray.400" : "transparent"
                        }
                        _hover={{
                          borderColor:
                            pageItem.value === page ? "blue.300" : "gray.600",
                        }}
                        _active={{
                          borderColor:
                            pageItem.value === page ? "blue.500" : "gray.700",
                        }}
                      >
                        {pageItem.value}
                      </IconButton>
                    )}
                  />

                  <Pagination.NextTrigger asChild>
                    <IconButton aria-label="Próxima página">
                      <LuChevronRight />
                    </IconButton>
                  </Pagination.NextTrigger>
                </ButtonGroup>
              </Pagination.Root>
            </Flex>
          )}
        {/* Show pagination info
        {totalItems > 0 && (
          <Text color="gray.400" fontSize="sm" textAlign="center">
            Mostrando {startPages + 1} a {Math.min(endPages, totalItems)} de{" "}
            {totalItems} fundamentos
            {searchTerm && ` (filtrado)`}
          </Text>
        )} */}
      </Stack>

      <BasicModal
        open={isEditModalOpen}
        setOpen={setIsEditModalOpen}
        title="Editar Fundamento"
        size="md"
        asForm={true}
        handleSubmit={handleSubmit(handleEditFoundation)}
        isSubmitting={isSubmitting}
        confirmText="Salvar"
        cancelText="Cancelar"
        placement="center"
      >
        <VStack gap={4} align={"stretch"}>
          <Field.Root invalid={!!errors.name}>
            <Field.Label color={"white"}>Nome do Fundamento</Field.Label>
            <Input
              placeholder="Digite o nome do fundamento"
              bg={"gray.700"}
              border={"1px solid"}
              borderColor={"gray.600"}
              color={"white"}
              _placeholder={{ color: "gray.400" }}
              _focus={{
                borderColor: "blue.400",
                boxShadow: "0 0 0 1px #3182ce",
              }}
              {...register("name")}
            />
            <Field.ErrorText>{errors.name?.message}</Field.ErrorText>
          </Field.Root>
        </VStack>
      </BasicModal>
    </Flex>
  );
}
