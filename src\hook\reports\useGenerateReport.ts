import { api } from "@/services/api";
import { useMutation } from "@tanstack/react-query";
import {
  ReportGenerationRequest,
  ReportGenerationResponse
} from "@/app/(app)/management/reports/data/types";
import { toaster } from "@/components/ui/toaster";
import { AxiosError } from "axios";
import { ApiErrorInputDTO } from "@/utils/types/DTO/api-error.dto";

async function generateReport(data: ReportGenerationRequest): Promise<ReportGenerationResponse> {
  const response = await api.post<ReportGenerationResponse>("/management/report", data);
  return response.data;
}

export function useGenerateReport() {
  return useMutation({
    mutationFn: generateReport,
    onSuccess: () => {
      toaster.success({
        title: "Relatório gerado com sucesso!",
        description: "Os dados do relatório foram processados.",
      });
    },
    onError: (error: AxiosError<ApiErrorInputDTO>) => {
      toaster.error({
        title: "Erro ao gerar relatório",
        description: error?.response?.data?.message || "Erro inesperado ao gerar o relatório",
      });
    },
  });
}
