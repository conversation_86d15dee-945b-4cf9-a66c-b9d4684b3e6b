"use client";

import {
  Box,
  Flex,
  HStack,
  Input,
  Stack,
  Table,
  Text,
  VStack,
  Field,
  useFilter,
  useListCollection,
  Pagination,
  ButtonGroup,
  IconButton,
} from "@chakra-ui/react";
import {
  LuSearch,
  LuPencil,
  LuChevronLeft,
  LuChevronRight,
} from "react-icons/lu";
import { useState } from "react";
import { useMutation, useQuery } from "@tanstack/react-query";
import { api } from "@/services/api";
import { useForm } from "react-hook-form";
import * as yup from "yup";
import { toaster } from "@/components/ui/toaster";
import DefaultButton from "@/components/global/buttons/button";
import { queryClient } from "@/services/queryClient";
import {
  GetAllProficiencyDto,
  GetProficiencyDto,
} from "@/utils/types/DTO/profeciency/profeciency.dto";
import { yupResolver } from "@hookform/resolvers/yup";
import { formatInTimeZone } from "date-fns-tz";
import BasicModal from "@/components/global/modal/basic-modal";
import { useGetAllProficiency } from "@/hook/proficiency/useGetAllProficiency";

const NewProficiencySchema = yup.object().shape({
  name: yup.string().required("O nome é obrigatório"),
  value: yup.string().required("O valor é obrigatório"),
  minimumWeighted: yup.string().required("O valor mínimo é obrigatório"),
  maximumWeighted: yup.string().required("O valor máximo é obrigatório"),
});

type ProficiencyFormData = {
  name: string;
  value: string;
  minimumWeighted: string;
  maximumWeighted: string;
};

export default function Proficiency() {
  const [searchTerm, setSearchTerm] = useState("");
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedProficiency, setSelectedProficiency] =
    useState<GetProficiencyDto | null>(null);
  const [page, setPage] = useState(1);

  const { data: proficiencyData } = useGetAllProficiency({
    searchTerm,
    page,
    limit: 6,
  });

  const { contains } = useFilter({ sensitivity: "base" });

  const { collection, filter } = useListCollection({
    initialItems: [],
    filter: contains,
  });

  const {
    register,
    handleSubmit,
    reset,
    setValue,
    formState: { errors, isSubmitting },
  } = useForm<ProficiencyFormData>({
    resolver: yupResolver(NewProficiencySchema),
    defaultValues: {
      name: "",
      value: "",
      minimumWeighted: "",
      maximumWeighted: "",
    },
  });

  const editProficiency = useMutation({
    mutationFn: async (data: ProficiencyFormData) => {
      await api.patch(
        `/management/proficiency/${selectedProficiency?.secureId}`,
        data
      );
    },
    onSuccess: () => {
      toaster.success({
        title: "Proficiência editada com sucesso!",
      });
    },
  });

  const handleOpenEditModal = (proficiency: GetProficiencyDto) => {
    filter("");
    setSelectedProficiency(proficiency);
    setValue("name", proficiency.name);
    setValue("value", proficiency.value?.toString() || "");
    setValue("minimumWeighted", proficiency.minimumWeighted?.toString() || "");
    setValue("maximumWeighted", proficiency.maximumWeighted?.toString() || "");
    setIsEditModalOpen(true);
  };

  const handleEditProficiency = async (data: ProficiencyFormData) => {
    try {
      await editProficiency.mutateAsync(data);
      queryClient.invalidateQueries({ queryKey: ["proficiency"] });
      setIsEditModalOpen(false);
      filter("");
      reset();
    } catch (e) {
      console.log("Erro ao editar proficiência", e);
    }
  };

  return (
    <Flex flex={1} position={"relative"} overflow={"hidden"} p={4}>
      <Stack w={"100%"} gap={6}>
        {/* Header */}
        <Text fontSize={"2xl"} fontWeight={"bold"} color={"white"}>
          Proficiência
        </Text>

        {/* Search and Filters */}
        <HStack gap={4} justify={"space-between"}>
          <Box position={"relative"} flex={1} maxW={"400px"}>
            <Input
              placeholder={"Buscar..."}
              bg={"gray.800"}
              border={"1px solid"}
              borderColor={"gray.600"}
              color={"white"}
              _placeholder={{ color: "gray.400" }}
              _focus={{
                borderColor: "blue.400",
                boxShadow: "0 0 0 1px #3182ce",
              }}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            <Box
              position="absolute"
              right={3}
              top="50%"
              transform="translateY(-50%)"
              color="gray.400"
            >
              <LuSearch />
            </Box>
          </Box>
        </HStack>

        {/* Table */}
        <Box
          bg={"gray.800"}
          border={"1px solid"}
          borderColor={"gray.600"}
          borderRadius={"lg"}
          overflowX={"auto"}
        >
          <Table.Root size={"md"} variant={"outline"}>
            <Table.Header bg={"gray.700"}>
              <Table.Row>
                <Table.ColumnHeader color={"white"} fontWeight={"bold"}>
                  Nome
                </Table.ColumnHeader>
                <Table.ColumnHeader color={"white"} fontWeight={"bold"}>
                  Valor
                </Table.ColumnHeader>
                <Table.ColumnHeader color={"white"} fontWeight={"bold"}>
                  Min. Ponderada
                </Table.ColumnHeader>
                <Table.ColumnHeader color={"white"} fontWeight={"bold"}>
                  Max. Ponderada
                </Table.ColumnHeader>
                <Table.ColumnHeader color={"white"} fontWeight={"bold"}>
                  Data de Criação
                </Table.ColumnHeader>
                <Table.ColumnHeader color={"white"} fontWeight={"bold"}>
                  <Flex justify={"center"}>Ação</Flex>
                </Table.ColumnHeader>
              </Table.Row>
            </Table.Header>

            <Table.Body>
              {proficiencyData?.data && proficiencyData?.data.length > 0 ? (
                proficiencyData?.data.map((item: GetProficiencyDto) => (
                  <Table.Row key={item.secureId} _hover={{ bg: "gray.700" }}>
                    <Table.Cell>
                      <Text color={"gray.300"} fontWeight={"medium"}>
                        {item.name}
                      </Text>
                    </Table.Cell>
                    <Table.Cell>
                      <Text color={"gray.300"} fontWeight={"medium"}>
                        {item.value}
                      </Text>
                    </Table.Cell>
                    <Table.Cell>
                      <Text color={"gray.300"} fontWeight={"medium"}>
                        {item.minimumWeighted}
                      </Text>
                    </Table.Cell>
                    <Table.Cell>
                      <Text color={"gray.300"} fontWeight={"medium"}>
                        {item.maximumWeighted}
                      </Text>
                    </Table.Cell>
                    <Table.Cell>
                      <Text color={"gray.300"} fontWeight={"medium"}>
                        {formatInTimeZone(
                          item.createdAt,
                          "America/Sao_Paulo",
                          "dd/MM/yyyy HH:mm:ss"
                        )}
                      </Text>
                    </Table.Cell>
                    <Table.Cell>
                      <HStack gap={2} justifyContent={"center"}>
                        <DefaultButton
                          tooltipContent="Editar"
                          buttonColor="#156082"
                          size={"sm"}
                          onClick={() => handleOpenEditModal(item)}
                        >
                          <LuPencil />
                        </DefaultButton>
                      </HStack>
                    </Table.Cell>
                  </Table.Row>
                ))
              ) : (
                <Table.Row>
                  <Table.Cell colSpan={5}>
                    <Text color={"gray.400"} textAlign={"center"} py={4}>
                      Nenhuma proficiência encontrada
                    </Text>
                  </Table.Cell>
                </Table.Row>
              )}
            </Table.Body>
          </Table.Root>
        </Box>

        {/* Pagination */}
        {proficiencyData?.meta?.TotalPages &&
          proficiencyData?.meta.TotalPages > 0 && (
            <Flex justifyContent="flex-end" mr={10}>
              <Pagination.Root
                count={proficiencyData?.meta?.TotalItems || 0}
                pageSize={proficiencyData?.meta?.ItemsPerPage || 0}
                page={proficiencyData?.meta?.CurrentPage}
                onPageChange={(e) => setPage(e.page)}
              >
                <ButtonGroup variant="ghost" size="sm">
                  <Pagination.PrevTrigger asChild>
                    <IconButton arial-label="Página anterior">
                      <LuChevronLeft />
                    </IconButton>
                  </Pagination.PrevTrigger>

                  <Pagination.Items
                    render={(pageItem) => (
                      <IconButton
                        key={pageItem.value}
                        variant={pageItem.value === page ? "outline" : "ghost"}
                        aria-label={`Ir para a página ${pageItem.value}`}
                        borderColor={
                          pageItem.value === page ? "gray.400" : "transparent"
                        }
                        _hover={{
                          borderColor:
                            pageItem.value === page ? "blue.300" : "gray.600",
                        }}
                        _active={{
                          borderColor:
                            pageItem.value === page ? "blue.500" : "gray.700",
                        }}
                      >
                        {pageItem.value}
                      </IconButton>
                    )}
                  />

                  <Pagination.NextTrigger asChild>
                    <IconButton aria-label="Próxima página">
                      <LuChevronRight />
                    </IconButton>
                  </Pagination.NextTrigger>
                </ButtonGroup>
              </Pagination.Root>
            </Flex>
          )}

        {/*{totalItems > 0 && (
					<Text color="gray.400" fontSize="sm" textAlign="center">
							Mostrando {startPages + 1} a {Math.min(endPages, totalItems)} de {totalItems} proficiências
						{searchTerm && ` (filtrado)`}
					</Text>
				)}*/}
      </Stack>

      {/* Edit Proficiency Modal */}
      <BasicModal
        open={isEditModalOpen}
        setOpen={setIsEditModalOpen}
        title="Editar Proficiência"
        size="md"
        asForm={true}
        handleSubmit={handleSubmit(handleEditProficiency)}
        isSubmitting={isSubmitting}
        confirmText="Salvar"
        cancelText="Cancelar"
        placement="center"
      >
        <VStack gap={4} align="stretch">
          <Field.Root invalid={!!errors.name}>
            <Field.Label color="white">Nome da Proficiência</Field.Label>
            <Input
              placeholder="Digite o nome da proficiência"
              bg="gray.700"
              border="1px solid"
              borderColor="gray.600"
              color="white"
              _placeholder={{ color: "gray.400" }}
              _focus={{
                borderColor: "blue.400",
                boxShadow: "0 0 0 1px #3182ce",
              }}
              {...register("name")}
            />
            <Field.ErrorText>{errors.name?.message}</Field.ErrorText>
          </Field.Root>

          <Field.Root invalid={!!errors.value}>
            <Field.Label color="white">Valor</Field.Label>
            <Input
              placeholder="Digite o valor"
              bg="gray.700"
              border="1px solid"
              borderColor="gray.600"
              color="white"
              _placeholder={{ color: "gray.400" }}
              _focus={{
                borderColor: "blue.400",
                boxShadow: "0 0 0 1px #3182ce",
              }}
              {...register("value")}
            />
            <Field.ErrorText>{errors.value?.message}</Field.ErrorText>
          </Field.Root>

          <Field.Root invalid={!!errors.minimumWeighted}>
            <Field.Label color="white">Valor Mínimo</Field.Label>
            <Input
              placeholder="Digite o valor mínimo"
              bg="gray.700"
              border="1px solid"
              borderColor="gray.600"
              color="white"
              _placeholder={{ color: "gray.400" }}
              _focus={{
                borderColor: "blue.400",
                boxShadow: "0 0 0 1px #3182ce",
              }}
              {...register("minimumWeighted")}
            />
            <Field.ErrorText>{errors.minimumWeighted?.message}</Field.ErrorText>
          </Field.Root>

          <Field.Root invalid={!!errors.maximumWeighted}>
            <Field.Label color="white">Valor Máximo</Field.Label>
            <Input
              placeholder="Digite o valor máximo"
              bg="gray.700"
              border="1px solid"
              borderColor="gray.600"
              color="white"
              _placeholder={{ color: "gray.400" }}
              _focus={{
                borderColor: "blue.400",
                boxShadow: "0 0 0 1px #3182ce",
              }}
              {...register("maximumWeighted")}
            />
            <Field.ErrorText>{errors.maximumWeighted?.message}</Field.ErrorText>
          </Field.Root>
        </VStack>
      </BasicModal>
    </Flex>
  );
}
