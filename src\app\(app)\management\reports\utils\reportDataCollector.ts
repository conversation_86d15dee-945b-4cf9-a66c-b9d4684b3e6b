import { 
  Segment, 
  ReportFilter, 
  ReportGenerationRequest 
} from "../data/types";

/**
 * Converts selected rounds to the API format
 */
export function convertRoundsToApiFormat(selectedRounds: Set<string>): "checkIn" | "checkOut" | "both" {
  const hasCheckIn = selectedRounds.has("rodada1");
  const hasCheckOut = selectedRounds.has("rodada2");
  const hasBoth = selectedRounds.has("rodada");

  if (hasBoth || (hasCheckIn && hasCheckOut)) {
    return "both";
  } else if (hasCheckIn) {
    return "checkIn";
  } else if (hasCheckOut) {
    return "checkOut";
  } else {
    // Default to checkIn if no selection
    return "checkIn";
  }
}

/**
 * Collects position filters from selected items and hierarchy data
 */
export function collectPositionFilters(
  selectedItems: Set<string>,
  hierarchyData: Segment[]
): ReportFilter[] {
  const filters: ReportFilter[] = [];

  // Iterate through all segments to find selected positions
  hierarchyData.forEach((segment) => {
    segment.superintendencias.forEach((superintendency) => {
      superintendency.gerencias.forEach((management) => {
        management.cargos.forEach((position) => {
          // Check if this position is selected
          if (selectedItems.has(position.secureId)) {
            filters.push({
              segment: segment.secureId,
              superintendency: superintendency.secureId,
              management: management.secureId,
              position: position.secureId,
            });
          }
        });
      });
    });
  });

  return filters;
}

/**
 * Validates if the required selections are made for report generation
 */
export function validateReportSelections(
  selectedRounds: Set<string>,
  selectedItems: Set<string>,
  hierarchyData: Segment[]
): { isValid: boolean; message?: string } {
  // Check if at least one round is selected
  const hasRoundSelection = selectedRounds.size > 0;
  if (!hasRoundSelection) {
    return {
      isValid: false,
      message: "Selecione pelo menos uma rodada (Check-in ou Check-out)",
    };
  }

  // Check if at least one position is selected
  const positionFilters = collectPositionFilters(selectedItems, hierarchyData);
  if (positionFilters.length === 0) {
    return {
      isValid: false,
      message: "Selecione pelo menos um cargo para gerar o relatório",
    };
  }

  return { isValid: true };
}

/**
 * Creates the complete report generation request payload
 */
export function createReportRequest(
  selectedRounds: Set<string>,
  selectedItems: Set<string>,
  hierarchyData: Segment[]
): ReportGenerationRequest {
  const rounds = convertRoundsToApiFormat(selectedRounds);
  const filters = collectPositionFilters(selectedItems, hierarchyData);

  return {
    rounds,
    filters,
  };
}
