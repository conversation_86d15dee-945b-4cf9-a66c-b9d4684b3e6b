import { useState, useCallback } from "react";
import { UseRoundSelectionReturn } from "../data/types";

export function useRoundSelection(): UseRoundSelectionReturn {
  const [selectedRounds, setSelectedRounds] = useState<Set<string>>(new Set());

  // Handle round selection
  const handleRoundChange = useCallback(
    (round: string, checked: boolean) => {
      const newRounds = new Set(selectedRounds);
      if (checked) {
        newRounds.add(round);
        // If "Rodada" is selected, select both Rodada 1 and Rodada 2
        if (round === "rodada") {
          newRounds.add("rodada1");
          newRounds.add("rodada2");
        }
      } else {
        newRounds.delete(round);
        // If "Rodada" is deselected, deselect both Rodada 1 and Rodada 2
        if (round === "rodada") {
          newRounds.delete("rodada1");
          newRounds.delete("rodada2");
        }
      }
      setSelectedRounds(newRounds);
    },
    [selectedRounds]
  );

  return {
    selectedRounds,
    handleRoundChange,
  };
}
