"use client";
import Defa<PERSON><PERSON><PERSON>on from "@/components/global/buttons/button";
import {
  Box,
  ButtonGroup,
  Collapsible,
  Field,
  Flex,
  HStack,
  Icon,
  IconButton,
  Input,
  InputGroup,
  Pagination,
  Stack,
  Table,
  Tabs,
  Text,
  Textarea,
  VStack,
} from "@chakra-ui/react";
import { Fragment, useState } from "react";
import {
  LuPlus,
  LuTrash2,
  LuPencil,
  LuChevronDown,
  LuChevronRight,
  LuChevronLeft,
} from "react-icons/lu";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { formatInTimeZone } from "date-fns-tz";
import BasicModal from "@/components/global/modal/basic-modal";
import { useMutation } from "@tanstack/react-query";
import FormCombobox from "@/components/global/combobox/form-combobox";
import { useGetAllQuestions } from "@/hook/questions/useGetAllQuestions";
import { GetQuestionDto } from "@/utils/types/DTO/questions/questions.dto";
import { useGetAllVideos } from "@/hook/videos/useGetAllVideos";
import { toaster } from "@/components/ui/toaster";
import { api } from "@/services/api";
import { queryClient } from "@/services/queryClient";
import { useGetAllQuestionsChoices } from "@/hook/questions/useGetAllQuestionsChoices";
import { useGetAllVariablesPercentage } from "@/hook/variables/useGetAllVariablesPercentage";
import { useGetAllProficiency } from "@/hook/proficiency/useGetAllProficiency";

type AddChoiceFormData = {
  name: string;
  order: number;
  proficiencyId: string;
};

const AddChoiceSchema = yup.object().shape({
  name: yup.string().required("O nome da alternativa é obrigatório."),
  order: yup
    .number()
    .typeError("A ordem deve ter um valor válido.")
    .min(0, "A ordem deve ser maior ou igual a 0.")
    .required("A ordem é obrigatória."),
  proficiencyId: yup.string().required("A proficiência é obrigatória."),
});

type VariableItem = {
  variable: string;
  percent: number;
};

type NewQuestionFormData = {
  essayStatement: string;
  objectiveStatement: string;
  variables: VariableItem[];
  videoId: string;
  order: number;
  questionType: "essay" | "objective";
  // Temporary fields for adding variables
  selectedVariable?: string;
  variablePercent?: number;
};

const VariableItemSchema = yup.object().shape({
  variable: yup.string().required("A variável é obrigatória."),
  percent: yup
    .number()
    .typeError("A porcentagem deve ser um número.")
    .min(0, "A porcentagem não pode ser negativa.")
    .max(100, "A porcentagem não pode ser maior que 100.")
    .required("A porcentagem é obrigatória."),
});

const NewQuestionSchema = yup.object().shape({
  essayStatement: yup
    .string()
    .required("O enunciado da questão dissertativa é obrigatório."),
  objectiveStatement: yup
    .string()
    .required("O enunciado da questão objetiva é obrigatório."),
  variables: yup
    .array()
    .of(VariableItemSchema)
    .min(1, "Adicione pelo menos uma variável.")
    .required("Adicione pelo menos uma variável."),
  videoId: yup.string().required("Vídeo deve ser selecionado."),
  order: yup
    .number()
    .typeError("A ordem deve ser um número.")
    .positive("A ordem deve ser um número positivo.")
    .integer("A ordem deve ser um número inteiro.")
    .required("A ordem é obrigatória."),
  questionType: yup.string().oneOf(["essay", "objective"]).notRequired(),
  selectedVariable: yup.string().notRequired(),
  variablePercent: yup.number().notRequired(),
});

const orderOptions = [
  { label: "A", value: "0" },
  { label: "B", value: "1" },
  { label: "C", value: "2" },
  { label: "D", value: "3" },
  { label: "E", value: "4" },
];

export default function Questions() {
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isAddChoiceModalOpen, setIsAddChoiceModalOpen] = useState(false);
  const [isEditChoiceModalOpen, setIsEditChoiceModalOpen] = useState(false);
  const [isDeleteChoiceModalOpen, setIsDeleteChoiceModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [expandedRows, setExpandedRows] = useState<string[]>([]);
  const [selectedQuestion, setSelectedQuestion] =
    useState<GetQuestionDto | null>(null);
  const [selectedChoice, setSelectedChoice] = useState<any>(null);
  const [currentQuestionType, setCurrentQuestionType] = useState<
    "essay" | "objective"
  >("essay");
  const [expandedQuestionId, setExpandedQuestionId] = useState<string>("");
  const [initialVariablesInEditMode, setInitialVariablesInEditMode] = useState<
    VariableItem[]
  >([]);
  const [page, setPage] = useState(1);

  const { data: questionsData } = useGetAllQuestions({
    page: page,
    limit: 6,
  });
  const { data: choicesData } = useGetAllQuestionsChoices(expandedQuestionId);
  const { data: videosData } = useGetAllVideos({});
  const { data: variablesData } = useGetAllVariablesPercentage();
  const { data: proficiencyData } = useGetAllProficiency({});

  const proficiencies = proficiencyData?.data || [];

  const getOrderLetter = (order: number): string => {
    const letters = ["A", "B", "C", "D", "E"];
    // Verifica se a ordem está dentro do intervalo esperado
    if (order >= 0 && order < letters.length) {
      return letters[order];
    }
    // Retorna o próprio número como string se for um valor inesperado
    return order.toString();
  };

  const {
    register,
    handleSubmit,
    reset,
    setValue,
    watch,
    formState,
    formState: { errors, isSubmitting },
  } = useForm({
    resolver: yupResolver(NewQuestionSchema),
    defaultValues: {
      essayStatement: "",
      objectiveStatement: "",
      variables: [],
      videoId: "",
      order: 1,
      questionType: "essay",
      selectedVariable: "",
      variablePercent: 0,
    },
  });

  const {
    register: registerChoice,
    handleSubmit: handleChoiceSubmit,
    reset: resetChoiceForm,
    setValue: setChoiceValue,
    watch: watchChoice,
    formState: { errors: choiceErrors, isSubmitting: isChoiceSubmitting },
  } = useForm<AddChoiceFormData>({
    resolver: yupResolver(AddChoiceSchema),
    defaultValues: {
      name: "",
      order: 0,
      proficiencyId: "",
    },
  });

  const {
    register: registerEditChoice,
    handleSubmit: handleEditChoiceSubmit,
    reset: resetEditChoiceForm,
    setValue: setEditChoiceValue,
    watch: watchEditChoice,
    formState: {
      errors: editChoiceErrors,
      isSubmitting: isEditChoiceSubmitting,
    },
  } = useForm<AddChoiceFormData>({
    resolver: yupResolver(AddChoiceSchema),
    defaultValues: {
      name: "",
      order: 0,
      proficiencyId: "",
    },
  });

  const handleAddVariable = () => {
    const selectedVariable = watch("selectedVariable");
    const variablePercent = watch("variablePercent");

    if (!selectedVariable || !variablePercent) {
      toaster.error({
        title: "Erro",
        description: "Selecione uma variável e informe a porcentagem.",
      });
      return;
    }

    const currentVariables = watch("variables") || [];

    // Check if variable is already added
    const isVariableAlreadyAdded = currentVariables.some(
      (v) => v.variable === selectedVariable
    );

    if (isVariableAlreadyAdded) {
      toaster.error({
        title: "Erro",
        description: "Esta variável já foi adicionada.",
      });
      return;
    }

    const newVariable: VariableItem = {
      variable: selectedVariable,
      percent: variablePercent,
    };

    setValue("variables", [...currentVariables, newVariable]);
    setValue("selectedVariable", "");
    setValue("variablePercent", 0);
  };

  const handleRemoveVariable = (index: number) => {
    const currentVariables = watch("variables") || [];
    const updatedVariables = currentVariables.filter((_, i) => i !== index);
    setValue("variables", updatedVariables);
  };

  const addQuestion = useMutation({
    mutationFn: async (data: any) => {
      const payload = {
        essayQuestion: {
          statement: data.essayStatement,
        },
        objectiveQuestion: {
          statement: data.objectiveStatement,
          variables: data.variables,
        },
        videoId: data.videoId,
        order: data.order,
      };

      await api.post("/management/question", payload);
    },
    onSuccess: () => {
      toaster.success({
        title: "Grupo de questões adicionado com sucesso!",
      });
    },
  });

  const addChoiceMutation = useMutation({
    mutationFn: async (data: AddChoiceFormData) => {
      const payload = {
        name: data.name,
        order: data.order,
        questionId: selectedQuestion?.objectiveQuestion?.secureId,
        proficiencyId: data.proficiencyId,
      };

      await api.post(`/management/question-choice`, payload);
    },
    onSuccess: () => {
      toaster.success({
        title: "Alternativa adicionada com sucesso!",
      });
      queryClient.invalidateQueries({ queryKey: ["questions"] });
      setIsAddChoiceModalOpen(false);
      resetChoiceForm();
    },
    onError: (error) => {
      // toaster.error({
      //   title: "Erro ao adicionar alternativa",
      //   description: error.message,
      // });
    },
  });

  const editChoiceMutation = useMutation({
    mutationFn: async (data: AddChoiceFormData) => {
      const payload = {
        name: data.name,
        order: data.order,
        questionId: selectedQuestion?.objectiveQuestion?.secureId,
        proficiencyId: data.proficiencyId,
      };

      await api.patch(
        `/management/question-choice/${selectedChoice?.secureId}`,
        payload
      );
    },
    onSuccess: () => {
      toaster.success({
        title: "Alternativa editada com sucesso!",
      });
      queryClient.invalidateQueries({ queryKey: ["questions"] });
      setIsEditChoiceModalOpen(false);
      resetEditChoiceForm();
    },
    onError: (error) => {
      // toaster.error({
      //   title: "Erro ao editar alternativa",
      //   description: error.message,
      // });
    },
  });

  const deleteChoiceMutation = useMutation({
    mutationFn: async () => {
      await api.delete(
        `/management/question-choice/${selectedChoice?.secureId}`
      );
    },
    onSuccess: () => {
      toaster.success({
        title: "Alternativa excluída com sucesso!",
      });
      queryClient.invalidateQueries({ queryKey: ["questions"] });
      setIsDeleteChoiceModalOpen(false);
    },
    onError: (error) => {
      // toaster.error({
      //   title: "Erro ao excluir alternativa",
      //   description: error.message,
      // });
    },
  });

  const handleAddChoice = async (data: AddChoiceFormData) => {
    if (choicesData?.data && choicesData.data.length >= 5) {
      toaster.error({
        title: "Limite de alternativas atingido",
        description: "Cada questão objetiva pode ter no máximo 5 alternativas.",
      });
      return;
    }
    await addChoiceMutation.mutateAsync(data);
  };

  const handleEditChoice = async (data: AddChoiceFormData) => {
    await editChoiceMutation.mutateAsync(data);
  };

  const handleDeleteChoice = async () => {
    await deleteChoiceMutation.mutateAsync();
  };

  const handleOpenAddChoiceModal = (question: GetQuestionDto) => {
    setSelectedQuestion(question);
    setIsAddChoiceModalOpen(true);
  };

  const handleOpenEditChoiceModal = (choice: any, question: GetQuestionDto) => {
    setSelectedChoice(choice);
    setSelectedQuestion(question);
    setEditChoiceValue("name", choice.name);
    setEditChoiceValue("order", choice.order);
    setEditChoiceValue("proficiencyId", choice.proficiency?.secureId || "");
    setIsEditChoiceModalOpen(true);
  };

  const handleOpenDeleteChoiceModal = (choice: any) => {
    setSelectedChoice(choice);
    setIsDeleteChoiceModalOpen(true);
  };

  const toggleRowExpansion = (question: GetQuestionDto) => {
    const questionId = question.secureId;
    const objectiveQuestionId = question.objectiveQuestion?.secureId || "";

    if (expandedRows.includes(questionId)) {
      // If already expanded, collapse it
      setExpandedRows([]);
      setExpandedQuestionId("");
    } else {
      // Expand only this row (accordion behavior)
      setExpandedRows([questionId]);
      setExpandedQuestionId(objectiveQuestionId);
    }
  };

  const editQuestion = useMutation({
    mutationFn: async (data: any) => {
      const payload = {
        essayQuestion: {
          statement: data.essayStatement,
        },
        objectiveQuestion: {
          statement: data.objectiveStatement,
          variables: data.variables,
        },
        videoId: data.videoId,
        order: data.order,
      };

      await api.patch(
        `/management/question/${selectedQuestion?.secureId}`,
        payload
      );
    },
    onSuccess: () => {
      toaster.success({
        title: "Grupo de questões editado com sucesso!",
      });
    },
  });

  const deleteQuestion = useMutation({
    mutationFn: async () => {
      await api.delete(`/management/question/${selectedQuestion?.secureId}`);
    },
    onSuccess: () => {
      toaster.success({
        title: "Questão excluída com sucesso!",
      });
    },
  });

  const handleAddQuestion = async (data: any) => {
    try {
      await addQuestion.mutateAsync(data);
      queryClient.invalidateQueries({ queryKey: ["questions"] });
      queryClient.invalidateQueries({ queryKey: ["variables"] });
      setIsAddModalOpen(false);
      reset();
    } catch (e) {
      console.error("Erro ao adicionar questão", e);
    }
  };

  const handleEditQuestion = async (data: any) => {
    try {
      await editQuestion.mutateAsync(data);
      queryClient.invalidateQueries({ queryKey: ["questions"] });
      queryClient.invalidateQueries({ queryKey: ["variables"] });
      setIsEditModalOpen(false);
      setInitialVariablesInEditMode([]);
      reset();
    } catch (e) {
      console.error("Erro ao editar questão", e);
    }
  };

  const handleDeleteQuestion = async () => {
    try {
      await deleteQuestion.mutateAsync();
      queryClient.invalidateQueries({ queryKey: ["questions"] });
      queryClient.invalidateQueries({ queryKey: ["variables"] });
      setIsDeleteModalOpen(false);
    } catch (e) {
      console.error("Erro ao excluir questão", e);
    }
  };

  const handleOpenAddModal = () => {
    reset();
    setIsAddModalOpen(true);
  };

  const handleOpenEditModal = (question: GetQuestionDto) => {
    const originalVariables =
      question.objectiveQuestion?.variables?.map((v) => ({
        variable: v.variable,
        percent: parseInt(v.percent) || 0,
      })) || [];
    setInitialVariablesInEditMode(originalVariables);

    setSelectedQuestion(question);
    setValue("essayStatement", question.essayQuestion?.statement || "");
    setValue("objectiveStatement", question.objectiveQuestion?.statement || "");
    setValue(
      "variables",
      question.objectiveQuestion?.variables?.map((v) => ({
        variable: v.variable,
        percent: parseInt(v.percent) || 0,
      })) || []
    );

    setValue("videoId", question.videoId);
    setValue("order", question.order);

    setIsEditModalOpen(true);
  };

  const handleOpenDeleteModal = (question: GetQuestionDto) => {
    setSelectedQuestion(question);
    setIsDeleteModalOpen(true);
  };

  return (
    <Flex flex={1} position={"relative"} overflow={"hidden"} p={4}>
      <Stack w="100%" gap={6}>
        {/* Header */}
        <Text fontSize="2xl" fontWeight="bold" color="white">
          Questões
        </Text>

        {/* Search and Filters */}
        <HStack justify="end">
          <DefaultButton onClick={handleOpenAddModal} size="md">
            <LuPlus />
            Adicionar Questões
          </DefaultButton>
        </HStack>

        {/* Table */}
        <Box
          bg="gray.800"
          borderRadius="lg"
          border="1px solid"
          borderColor="gray.600"
          overflowX="auto"
        >
          <Table.Root size="md" variant="outline">
            <Table.Header bg="gray.700">
              <Table.Row>
                <Table.ColumnHeader
                  color="white"
                  fontWeight="bold"
                ></Table.ColumnHeader>
                <Table.ColumnHeader color="white" fontWeight="bold">
                  Enunciado Dissertativa
                </Table.ColumnHeader>
                <Table.ColumnHeader color="white" fontWeight="bold">
                  Enunciado Objetiva
                </Table.ColumnHeader>
                <Table.ColumnHeader color="white" fontWeight="bold">
                  Ordem
                </Table.ColumnHeader>
                <Table.ColumnHeader color="white" fontWeight="bold">
                  Data de Criação
                </Table.ColumnHeader>
                <Table.ColumnHeader color="white" fontWeight="bold">
                  <Flex justify="center">Ações</Flex>
                </Table.ColumnHeader>
              </Table.Row>
            </Table.Header>
            <Table.Body>
              {questionsData?.data && questionsData?.data.length > 0 ? (
                questionsData?.data?.map((item) => (
                  <Fragment key={item.secureId}>
                    <Table.Row _hover={{ bg: "gray.700" }}>
                      <Table.Cell>
                        <DefaultButton
                          tooltipContent={
                            expandedRows.includes(item.secureId)
                              ? "Recolher Alternativas"
                              : "Expandir Alternativas"
                          }
                          bgColor={"gray.600"}
                          onClick={() => toggleRowExpansion(item)}
                          size="sm"
                          variant="ghost"
                        >
                          <Icon
                            as={
                              expandedRows.includes(item.secureId)
                                ? LuChevronDown
                                : LuChevronRight
                            }
                          />
                        </DefaultButton>
                      </Table.Cell>
                      <Table.Cell>
                        <Text
                          color="gray.300"
                          maxW={"600px"}
                          lineClamp={
                            expandedQuestionId ===
                            item.objectiveQuestion?.secureId
                              ? 1000
                              : 2
                          }
                        >
                          {item.essayQuestion.statement}
                        </Text>
                      </Table.Cell>
                      <Table.Cell>
                        <Text
                          color="gray.300"
                          maxW={"600px"}
                          lineClamp={
                            expandedQuestionId ===
                            item.objectiveQuestion?.secureId
                              ? 1000
                              : 2
                          }
                        >
                          {item.objectiveQuestion.statement}
                        </Text>
                      </Table.Cell>
                      <Table.Cell>
                        <Text color="gray.300">{item.order}</Text>
                      </Table.Cell>
                      <Table.Cell>
                        <Text color="gray.300" maxW="200px">
                          {formatInTimeZone(
                            item.createdAt,
                            "America/Sao_Paulo",
                            "dd/MM/yyyy HH:mm:ss"
                          )}
                        </Text>
                      </Table.Cell>
                      <Table.Cell>
                        <HStack gap={2} justifyContent={"center"}>
                          <DefaultButton
                            tooltipContent={
                              expandedQuestionId ===
                                item.objectiveQuestion?.secureId &&
                              choicesData?.data &&
                              choicesData.data.length >= 5
                                ? "Limite de 5 alternativas atingido"
                                : "Adicionar Alternativas"
                            }
                            buttonColor="green.600"
                            size="sm"
                            onClick={() => handleOpenAddChoiceModal(item)}
                            disabled={
                              (item.objectiveQuestion?.choicesCount || 0) >= 5
                            }
                          >
                            <LuPlus />
                          </DefaultButton>
                          <DefaultButton
                            tooltipContent="Editar"
                            buttonColor="#156082"
                            size="sm"
                            onClick={() => handleOpenEditModal(item)}
                          >
                            <LuPencil />
                          </DefaultButton>
                          <DefaultButton
                            tooltipContent="Excluir"
                            buttonColor="red.500"
                            size="sm"
                            onClick={() => handleOpenDeleteModal(item)}
                          >
                            <LuTrash2 />
                          </DefaultButton>
                        </HStack>
                      </Table.Cell>
                    </Table.Row>

                    {/* Collapsible Row */}
                    <Table.Row>
                      <Table.Cell colSpan={6} p={0} border="none">
                        <Collapsible.Root
                          open={expandedRows.includes(item.secureId)}
                        >
                          <Collapsible.Content>
                            <Box p={4} bg="gray.900">
                              <Text fontWeight="bold" color="white" mb={2}>
                                Alternativas:
                              </Text>
                              {choicesData?.data &&
                              choicesData.data.length > 0 ? (
                                <VStack align="stretch" gap={2}>
                                  {choicesData.data.map((choice) => (
                                    <HStack
                                      key={choice.secureId}
                                      justify="space-between"
                                      p={2}
                                      bg="gray.700"
                                      borderRadius="md"
                                    >
                                      <Text color="gray.300" flex="1">
                                        {choice.name}
                                      </Text>
                                      <Text
                                        color="gray.400"
                                        fontSize="sm"
                                        whiteSpace="nowrap"
                                      >
                                        {choice.proficiency?.name}
                                      </Text>
                                      <Text
                                        color="gray.400"
                                        fontSize="sm"
                                        whiteSpace="nowrap"
                                        ml={4}
                                      >
                                        Ordem: {getOrderLetter(choice.order)}
                                      </Text>
                                      <HStack gap={1} ml={4}>
                                        <DefaultButton
                                          tooltipContent="Editar Alternativa"
                                          buttonColor="#156082"
                                          size="xs"
                                          onClick={() =>
                                            handleOpenEditChoiceModal(
                                              choice,
                                              item
                                            )
                                          }
                                        >
                                          <LuPencil />
                                        </DefaultButton>
                                        <DefaultButton
                                          tooltipContent="Excluir Alternativa"
                                          buttonColor="red.500"
                                          size="xs"
                                          onClick={() =>
                                            handleOpenDeleteChoiceModal(choice)
                                          }
                                        >
                                          <LuTrash2 />
                                        </DefaultButton>
                                      </HStack>
                                    </HStack>
                                  ))}
                                </VStack>
                              ) : (
                                <Text color="gray.400">
                                  Nenhuma alternativa cadastrada.
                                </Text>
                              )}
                            </Box>
                          </Collapsible.Content>
                        </Collapsible.Root>
                      </Table.Cell>
                    </Table.Row>
                  </Fragment>
                ))
              ) : (
                <Table.Row>
                  <Table.Cell colSpan={6}>
                    <Text color="gray.400" textAlign="center" py={4}>
                      Nenhuma questão encontrada
                    </Text>
                  </Table.Cell>
                </Table.Row>
              )}
            </Table.Body>
          </Table.Root>
        </Box>
        {questionsData?.meta?.TotalPages &&
          questionsData?.meta?.TotalPages > 0 && (
            <Flex justifyContent={"flex-end"} mr={10}>
              <Pagination.Root
                count={questionsData?.meta?.TotalItems || 0}
                pageSize={questionsData?.meta?.ItemsPerPage || 0}
                page={questionsData?.meta?.CurrentPage}
                onPageChange={(e) => setPage(e.page)}
              >
                <ButtonGroup variant="ghost" size="sm">
                  <Pagination.PrevTrigger asChild>
                    <IconButton aria-label="Página anterior">
                      <LuChevronLeft />
                    </IconButton>
                  </Pagination.PrevTrigger>

                  <Pagination.Items
                    render={(pageItem) => (
                      <IconButton
                        key={pageItem.value}
                        variant={pageItem.value === page ? "outline" : "ghost"}
                        aria-label={`Ir para a página ${pageItem.value}`}
                        borderColor={
                          pageItem.value === page ? "gray.400" : "transparent"
                        }
                        _hover={{
                          borderColor:
                            pageItem.value === page ? "blue.300" : "gray.600",
                        }}
                        _active={{
                          borderColor:
                            pageItem.value === page ? "blue.500" : "gray.700",
                        }}
                      >
                        {pageItem.value}
                      </IconButton>
                    )}
                  />

                  <Pagination.NextTrigger asChild>
                    <IconButton aria-label="Próxima página">
                      <LuChevronRight />
                    </IconButton>
                  </Pagination.NextTrigger>
                </ButtonGroup>
              </Pagination.Root>
            </Flex>
          )}
      </Stack>

      {/* Add Question Modal */}
      <BasicModal
        open={isAddModalOpen}
        setOpen={setIsAddModalOpen}
        title="Adicionar Grupo de Questões"
        size="lg"
        asForm={true}
        handleSubmit={handleSubmit(handleAddQuestion)}
        isSubmitting={formState.isSubmitting}
        confirmText="Adicionar"
        cancelText="Cancelar"
        placement="center"
      >
        <HStack gap={4} align="stretch">
          <FormCombobox
            label="Vídeo de Introdução"
            placeholder="Selecione um vídeo"
            options={
              videosData?.data?.map((video) => ({
                label: video.title,
                value: video.secureId,
              })) || []
            }
            value={watch("videoId")}
            onValueChange={(value) => setValue("videoId", value as string)}
            error={errors.videoId}
            isInvalid={!!errors.videoId}
          />

          <Field.Root invalid={!!errors.order}>
            <Field.Label color="white">Ordem</Field.Label>
            <Input
              min={1}
              type="number"
              placeholder="Digite a ordem da questão"
              bg="gray.700"
              border="1px solid"
              borderColor="gray.600"
              color="white"
              _placeholder={{ color: "gray.400" }}
              _focus={{
                borderColor: "blue.400",
                boxShadow: "0 0 0 1px #3182ce",
              }}
              {...register("order")}
            />
            <Field.ErrorText>{errors.order?.message}</Field.ErrorText>
          </Field.Root>
        </HStack>
        <Tabs.Root
          defaultValue="essay"
          variant={"subtle"}
          mt={4}
          onValueChange={(details) => {
            setValue("questionType", details.value as "essay" | "objective");
            setCurrentQuestionType(details.value as "essay" | "objective");
          }}
        >
          <Tabs.List gap={4}>
            <Tabs.Trigger
              value="essay"
              outline={"1px solid rgb(134, 134, 141, 0.5)"}
            >
              Dissertativa
            </Tabs.Trigger>
            <Tabs.Trigger
              value="objective"
              outline={"1px solid rgb(134, 134, 141, 0.5)"}
            >
              Objetiva
            </Tabs.Trigger>
          </Tabs.List>
          <Tabs.Content value="essay">
            <VStack gap={4} align="stretch">
              <Field.Root invalid={!!errors.essayStatement}>
                <Field.Label color="white">Enunciado</Field.Label>
                <Textarea
                  placeholder="Digite o enunciado da questão dissertativa"
                  bg="gray.700"
                  border="1px solid"
                  borderColor="gray.600"
                  color="white"
                  _placeholder={{ color: "gray.400" }}
                  _focus={{
                    borderColor: "blue.400",
                    boxShadow: "0 0 0 1px #3182ce",
                  }}
                  {...register("essayStatement")}
                />
                <Field.ErrorText>
                  {errors.essayStatement?.message}
                </Field.ErrorText>
              </Field.Root>
            </VStack>
          </Tabs.Content>
          <Tabs.Content value="objective">
            <VStack gap={4} align="stretch">
              <Field.Root invalid={!!errors.objectiveStatement}>
                <Field.Label color="white">Enunciado</Field.Label>
                <Textarea
                  placeholder="Digite o enunciado da questão objetiva"
                  bg="gray.700"
                  border="1px solid"
                  borderColor="gray.600"
                  color="white"
                  _placeholder={{ color: "gray.400" }}
                  _focus={{
                    borderColor: "blue.400",
                    boxShadow: "0 0 0 1px #3182ce",
                  }}
                  {...register("objectiveStatement")}
                />
                <Field.ErrorText>
                  {errors.objectiveStatement?.message}
                </Field.ErrorText>
              </Field.Root>

              <HStack gap={4} align="end">
                <FormCombobox
                  label="Variável"
                  placeholder="Selecione uma variável"
                  options={
                    variablesData?.map((variable) => ({
                      label: variable.name,
                      value: variable.secureId,
                    })) || []
                  }
                  value={watch("selectedVariable") || ""}
                  onValueChange={(value) =>
                    setValue("selectedVariable", value as string)
                  }
                />

                <Field.Root invalid={!!errors.variablePercent}>
                  <Field.Label color="white">Porcentagem</Field.Label>
                  <InputGroup endElement={<Text>%</Text>}>
                    <Input
                      type="number"
                      placeholder="0"
                      bg="gray.700"
                      border="1px solid"
                      borderColor="gray.600"
                      color="white"
                      _placeholder={{ color: "gray.400" }}
                      _focus={{
                        borderColor: "blue.400",
                        boxShadow: "0 0 0 1px #3182ce",
                      }}
                      {...register("variablePercent", { valueAsNumber: true })}
                    />
                  </InputGroup>
                  <Field.ErrorText>
                    {errors.variablePercent?.message}
                  </Field.ErrorText>
                </Field.Root>

                <DefaultButton
                  tooltipContent="Adicionar Variável"
                  buttonColor="#156082"
                  size="md"
                  onClick={handleAddVariable}
                  type="button"
                >
                  <LuPlus />
                </DefaultButton>
              </HStack>

              {/* Variables List */}
              {watch("variables") && watch("variables")!.length > 0 && (
                <Box>
                  <Box
                    bg="gray.700"
                    borderRadius="md"
                    border="1px solid"
                    borderColor="gray.600"
                    overflow="hidden"
                  >
                    <Table.Root size="sm" variant="outline">
                      <Table.Header bg="gray.600">
                        <Table.Row>
                          <Table.ColumnHeader
                            color="white"
                            fontWeight="bold"
                            fontSize="xs"
                          >
                            NOME
                          </Table.ColumnHeader>
                          <Table.ColumnHeader
                            color="white"
                            fontWeight="bold"
                            fontSize="xs"
                          >
                            PORCENTAGEM TOTAL
                          </Table.ColumnHeader>
                          <Table.ColumnHeader
                            color="white"
                            fontWeight="bold"
                            fontSize="xs"
                          >
                            PORCENTAGEM
                          </Table.ColumnHeader>
                          <Table.ColumnHeader
                            color="white"
                            fontWeight="bold"
                            fontSize="xs"
                          >
                            AÇÕES
                          </Table.ColumnHeader>
                        </Table.Row>
                      </Table.Header>
                      <Table.Body>
                        {watch("variables")!.map((variable, index) => {
                          const variableData = variablesData?.find(
                            (v) => v.secureId === variable.variable
                          );
                          const totalPercentage = variableData?.percentage || 0;
                          const questionPercentage = variable.percent;
                          const combinedPercentage =
                            totalPercentage + questionPercentage;

                          return (
                            <Table.Row key={index} _hover={{ bg: "gray.600" }}>
                              <Table.Cell>
                                <Text color="gray.300" fontSize="sm">
                                  {variableData?.name || variable.variable}
                                </Text>
                              </Table.Cell>
                              <Table.Cell>
                                <Text color="gray.300" fontSize="sm">
                                  {combinedPercentage}%
                                </Text>
                              </Table.Cell>
                              <Table.Cell>
                                <Text color="gray.300" fontSize="sm">
                                  {questionPercentage}%
                                </Text>
                              </Table.Cell>
                              <Table.Cell>
                                <DefaultButton
                                  size="xs"
                                  buttonColor="red.500"
                                  onClick={() => handleRemoveVariable(index)}
                                  type="button"
                                  tooltipContent="Remover Variável"
                                >
                                  <LuTrash2 />
                                </DefaultButton>
                              </Table.Cell>
                            </Table.Row>
                          );
                        })}
                      </Table.Body>
                    </Table.Root>
                  </Box>
                </Box>
              )}
              {errors.variables && (
                <Text color="red.400" fontSize="sm">
                  {errors.variables.message}
                </Text>
              )}
            </VStack>
          </Tabs.Content>
        </Tabs.Root>
      </BasicModal>

      {/* Edit Question Modal */}
      <BasicModal
        open={isEditModalOpen}
        setOpen={(isOpen) => {
          if (!isOpen) {
            setInitialVariablesInEditMode([]);
          }
          setIsEditModalOpen(isOpen);
        }}
        title="Editar Grupo de Questões"
        size="lg"
        asForm={true}
        handleSubmit={handleSubmit(handleEditQuestion)}
        isSubmitting={formState.isSubmitting}
        confirmText="Salvar"
        cancelText="Cancelar"
        placement="center"
      >
        <HStack gap={4} align="stretch">
          <FormCombobox
            label="Vídeo de Introdução"
            placeholder="Selecione um vídeo"
            options={
              videosData?.data?.map((video) => ({
                label: video.title,
                value: video.secureId,
              })) || []
            }
            value={watch("videoId")}
            onValueChange={(value) => setValue("videoId", value as string)}
            error={errors.videoId}
            isInvalid={!!errors.videoId}
          />

          <Field.Root invalid={!!errors.order}>
            <Field.Label color="white">Ordem</Field.Label>
            <Input
              type="number"
              placeholder="Digite a ordem da questão"
              bg="gray.700"
              border="1px solid"
              borderColor="gray.600"
              color="white"
              _placeholder={{ color: "gray.400" }}
              _focus={{
                borderColor: "blue.400",
                boxShadow: "0 0 0 1px #3182ce",
              }}
              {...register("order")}
            />
            <Field.ErrorText>{errors.order?.message}</Field.ErrorText>
          </Field.Root>
        </HStack>
        <Tabs.Root
          value={currentQuestionType}
          variant={"subtle"}
          mt={4}
          onValueChange={(details) => {
            setValue("questionType", details.value as "essay" | "objective");
            setCurrentQuestionType(details.value as "essay" | "objective");
          }}
        >
          <Tabs.List gap={4}>
            <Tabs.Trigger
              value="essay"
              outline={"1px solid rgb(134, 134, 141, 0.5)"}
            >
              Dissertativa
            </Tabs.Trigger>
            <Tabs.Trigger
              value="objective"
              outline={"1px solid rgb(134, 134, 141, 0.5)"}
            >
              Objetiva
            </Tabs.Trigger>
          </Tabs.List>
          <Tabs.Content value="essay">
            <VStack gap={4} align="stretch">
              <Field.Root invalid={!!errors.essayStatement}>
                <Field.Label color="white">Enunciado</Field.Label>
                <Textarea
                  placeholder="Digite o enunciado da questão dissertativa"
                  bg="gray.700"
                  border="1px solid"
                  borderColor="gray.600"
                  color="white"
                  _placeholder={{ color: "gray.400" }}
                  _focus={{
                    borderColor: "blue.400",
                    boxShadow: "0 0 0 1px #3182ce",
                  }}
                  {...register("essayStatement")}
                />
                <Field.ErrorText>
                  {errors.essayStatement?.message}
                </Field.ErrorText>
              </Field.Root>
            </VStack>
          </Tabs.Content>
          <Tabs.Content value="objective">
            <VStack gap={4} align="stretch">
              <Field.Root invalid={!!errors.objectiveStatement}>
                <Field.Label color="white">Enunciado</Field.Label>
                <Textarea
                  placeholder="Digite o enunciado da questão objetiva"
                  bg="gray.700"
                  border="1px solid"
                  borderColor="gray.600"
                  color="white"
                  _placeholder={{ color: "gray.400" }}
                  _focus={{
                    borderColor: "blue.400",
                    boxShadow: "0 0 0 1px #3182ce",
                  }}
                  {...register("objectiveStatement")}
                />
                <Field.ErrorText>
                  {errors.objectiveStatement?.message}
                </Field.ErrorText>
              </Field.Root>

              <HStack gap={4} align="end">
                <FormCombobox
                  label="Variável"
                  placeholder="Selecione uma variável"
                  options={
                    variablesData?.map((variable) => ({
                      label: variable.name,
                      value: variable.secureId,
                    })) || []
                  }
                  value={watch("selectedVariable") || ""}
                  onValueChange={(value) =>
                    setValue("selectedVariable", value as string)
                  }
                />

                <Field.Root invalid={!!errors.variablePercent}>
                  <Field.Label color="white">Porcentagem</Field.Label>
                  <InputGroup endElement={<Text>%</Text>}>
                    <Input
                      type="number"
                      placeholder="0"
                      bg="gray.700"
                      border="1px solid"
                      borderColor="gray.600"
                      color="white"
                      _placeholder={{ color: "gray.400" }}
                      _focus={{
                        borderColor: "blue.400",
                        boxShadow: "0 0 0 1px #3182ce",
                      }}
                      {...register("variablePercent", { valueAsNumber: true })}
                    />
                  </InputGroup>
                  <Field.ErrorText>
                    {errors.variablePercent?.message}
                  </Field.ErrorText>
                </Field.Root>

                <DefaultButton
                  tooltipContent="Adicionar Variável"
                  buttonColor="#156082"
                  size="md"
                  onClick={handleAddVariable}
                  type="button"
                >
                  <LuPlus />
                </DefaultButton>
              </HStack>

              {/* Variables List */}
              {watch("variables") && watch("variables")!.length > 0 && (
                <Box>
                  <Box
                    bg="gray.700"
                    borderRadius="md"
                    border="1px solid"
                    borderColor="gray.600"
                    overflow="hidden"
                  >
                    <Table.Root size="sm" variant="outline">
                      <Table.Header bg="gray.600">
                        <Table.Row>
                          <Table.ColumnHeader
                            color="white"
                            fontWeight="bold"
                            fontSize="xs"
                          >
                            NOME
                          </Table.ColumnHeader>
                          <Table.ColumnHeader
                            color="white"
                            fontWeight="bold"
                            fontSize="xs"
                          >
                            PORCENTAGEM TOTAL
                          </Table.ColumnHeader>
                          <Table.ColumnHeader
                            color="white"
                            fontWeight="bold"
                            fontSize="xs"
                          >
                            PORCENTAGEM
                          </Table.ColumnHeader>
                          <Table.ColumnHeader
                            color="white"
                            fontWeight="bold"
                            fontSize="xs"
                          >
                            AÇÕES
                          </Table.ColumnHeader>
                        </Table.Row>
                      </Table.Header>
                      <Table.Body>
                        {watch("variables")!.map((variable, index) => {
                          const variableData = variablesData?.find(
                            (v) => v.secureId === variable.variable
                          );
                          const totalPercentageFromDB =
                            variableData?.percentage || 0;
                          const questionPercentage = variable.percent;

                          const initialVar = initialVariablesInEditMode.find(
                            (v) => v.variable === variable.variable
                          );

                          const displayTotal = initialVar
                            ? totalPercentageFromDB -
                              initialVar.percent +
                              questionPercentage
                            : totalPercentageFromDB + questionPercentage;

                          return (
                            <Table.Row key={index} _hover={{ bg: "gray.600" }}>
                              <Table.Cell>
                                <Text color="gray.300" fontSize="sm">
                                  {variableData?.name || variable.variable}
                                </Text>
                              </Table.Cell>
                              <Table.Cell>
                                <Text color="gray.300" fontSize="sm">
                                  {displayTotal}%
                                </Text>
                              </Table.Cell>
                              <Table.Cell>
                                <Text color="gray.300" fontSize="sm">
                                  {questionPercentage}%
                                </Text>
                              </Table.Cell>
                              <Table.Cell>
                                <DefaultButton
                                  size="xs"
                                  buttonColor="red.500"
                                  onClick={() => handleRemoveVariable(index)}
                                  type="button"
                                  tooltipContent="Remover Variável"
                                >
                                  <LuTrash2 />
                                </DefaultButton>
                              </Table.Cell>
                            </Table.Row>
                          );
                        })}
                      </Table.Body>
                    </Table.Root>
                  </Box>
                </Box>
              )}

              {errors.variables && (
                <Text color="red.400" fontSize="sm">
                  {errors.variables.message}
                </Text>
              )}
            </VStack>
          </Tabs.Content>
        </Tabs.Root>
      </BasicModal>

      {/* Add Choice Modal */}
      <BasicModal
        open={isAddChoiceModalOpen}
        setOpen={setIsAddChoiceModalOpen}
        title="Adicionar Alternativa"
        size="md"
        asForm={true}
        handleSubmit={handleChoiceSubmit(handleAddChoice)}
        isSubmitting={isChoiceSubmitting}
        confirmText="Adicionar"
        cancelText="Cancelar"
      >
        <VStack gap={4}>
          <Field.Root invalid={!!choiceErrors.name}>
            <Field.Label color="white">Nome da Alternativa</Field.Label>
            <Textarea
              {...registerChoice("name")}
              placeholder="Digite o nome da alternativa"
              bg="gray.700"
              border="1px solid"
              borderColor="gray.600"
              color="white"
              _placeholder={{ color: "gray.400" }}
              _focus={{
                borderColor: "blue.400",
                boxShadow: "0 0 0 1px #3182ce",
              }}
            />
            <Field.ErrorText>{choiceErrors.name?.message}</Field.ErrorText>
          </Field.Root>

          <FormCombobox
            label="Ordem"
            placeholder="Selecione a ordem"
            options={orderOptions}
            value={watchChoice("order")?.toString()}
            onValueChange={(value) => {
              setChoiceValue("order", parseInt(value as string, 10), {
                shouldValidate: true,
              });
            }}
            error={choiceErrors.order}
            isInvalid={!!choiceErrors.order}
          />

          {/* <Field.Root invalid={!!choiceErrors.order}>
            <Field.Label color="white">Ordem</Field.Label>
            <Input
              {...registerChoice("order", { valueAsNumber: true })}
              type="number"
              min={0}
              placeholder="Digite a ordem da alternativa"
              bg="gray.700"
              border="1px solid"
              borderColor="gray.600"
              color="white"
              _placeholder={{ color: "gray.400" }}
              _focus={{
                borderColor: "blue.400",
                boxShadow: "0 0 0 1px #3182ce",
              }}
            />
            <Field.ErrorText>{choiceErrors.order?.message}</Field.ErrorText>
          </Field.Root> */}

          <FormCombobox
            label="Proficiência"
            placeholder="Selecione uma proficiência"
            options={proficiencies.map((proficiency) => ({
              label: proficiency.name,
              value: proficiency.secureId,
            }))}
            value={watchChoice("proficiencyId")}
            onValueChange={(value) =>
              setChoiceValue("proficiencyId", value as string)
            }
            error={choiceErrors.proficiencyId}
            isInvalid={!!choiceErrors.proficiencyId}
          />
        </VStack>
      </BasicModal>

      {/* Edit Choice Modal */}
      <BasicModal
        open={isEditChoiceModalOpen}
        setOpen={setIsEditChoiceModalOpen}
        title="Editar Alternativa"
        size="md"
        asForm={true}
        handleSubmit={handleEditChoiceSubmit(handleEditChoice)}
        isSubmitting={isEditChoiceSubmitting}
        confirmText="Salvar"
        cancelText="Cancelar"
      >
        <VStack gap={4}>
          <Field.Root invalid={!!editChoiceErrors.name}>
            <Field.Label color="white">Nome da Alternativa</Field.Label>
            <Textarea
              {...registerEditChoice("name")}
              placeholder="Digite o nome da alternativa"
              bg="gray.700"
              border="1px solid"
              borderColor="gray.600"
              color="white"
              _placeholder={{ color: "gray.400" }}
              _focus={{
                borderColor: "blue.400",
                boxShadow: "0 0 0 1px #3182ce",
              }}
            />
            <Field.ErrorText>{editChoiceErrors.name?.message}</Field.ErrorText>
          </Field.Root>

          <FormCombobox
            label="Ordem"
            placeholder="Selecione a ordem"
            options={orderOptions}
            value={watchEditChoice("order")?.toString()}
            onValueChange={(value) => {
              setEditChoiceValue("order", parseInt(value as string, 10), {
                shouldValidate: true,
              });
            }}
            error={editChoiceErrors.order}
            isInvalid={!!editChoiceErrors.order}
          />

          {/* <Field.Root invalid={!!editChoiceErrors.order}>
            <Field.Label color="white">Ordem</Field.Label>
            <Input
              {...registerEditChoice("order", { valueAsNumber: true })}
              type="number"
              min={0}
              placeholder="Digite a ordem da alternativa"
              bg="gray.700"
              border="1px solid"
              borderColor="gray.600"
              color="white"
              _placeholder={{ color: "gray.400" }}
              _focus={{
                borderColor: "blue.400",
                boxShadow: "0 0 0 1px #3182ce",
              }}
            />
            <Field.ErrorText>{editChoiceErrors.order?.message}</Field.ErrorText>
          </Field.Root> */}

          <FormCombobox
            label="Proficiência"
            placeholder="Selecione uma proficiência"
            options={proficiencies.map((proficiency) => ({
              label: proficiency.name,
              value: proficiency.secureId,
            }))}
            value={watchEditChoice("proficiencyId")}
            onValueChange={(value) =>
              setEditChoiceValue("proficiencyId", value as string)
            }
            error={editChoiceErrors.proficiencyId}
            isInvalid={!!editChoiceErrors.proficiencyId}
          />
        </VStack>
      </BasicModal>

      {/* Delete Choice Modal */}
      <BasicModal
        open={isDeleteChoiceModalOpen}
        setOpen={setIsDeleteChoiceModalOpen}
        title="Excluir Alternativa"
        size="sm"
        handleConfirm={handleDeleteChoice}
        confirmText="Excluir"
        cancelText="Cancelar"
        placement="center"
        confirmButtonColor="red.500"
      >
        <VStack gap={4} align="center">
          <Text fontSize="md" color="white" textAlign="center">
            Você tem certeza que deseja excluir esta alternativa?
          </Text>
          <Text fontSize="sm" color="gray.400" textAlign="center">
            Esta ação não pode ser desfeita.
          </Text>
        </VStack>
      </BasicModal>

      {/* Delete Question Modal */}
      <BasicModal
        open={isDeleteModalOpen}
        setOpen={setIsDeleteModalOpen}
        title="Excluir Questão"
        size="sm"
        handleConfirm={handleDeleteQuestion}
        confirmText="Excluir"
        cancelText="Cancelar"
        placement="center"
        confirmButtonColor="red.500"
      >
        <VStack gap={4} align="center">
          <Text fontSize="md" color="white" textAlign="center">
            Você tem certeza que deseja excluir essa questão?
          </Text>
          <Text fontSize="sm" color="gray.400" textAlign="center">
            Esta ação não pode ser desfeita.
          </Text>
        </VStack>
      </BasicModal>
    </Flex>
  );
}
